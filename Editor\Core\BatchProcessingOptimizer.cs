using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace AssetPipeline.Core
{
    /// <summary>
    /// 批量处理优化器 - 现实项目导向的批量导入优化
    /// 设计原则：简单有效，针对大型项目的实际使用场景
    /// </summary>
    public static class BatchProcessingOptimizer
    {
        /// <summary>
        /// 批量处理统计信息
        /// </summary>
        public class BatchStatistics
        {
            public int TotalFiles { get; set; }
            public int UniqueDirectories { get; set; }
            public int ProfileCacheHits { get; set; }
            public int ProcessorCacheHits { get; set; }
            public TimeSpan ProcessingTime { get; set; }
            
            public override string ToString()
            {
                return $"批量处理统计: {TotalFiles}个文件, {UniqueDirectories}个目录, " +
                       $"Profile缓存命中{ProfileCacheHits}次, 处理器缓存命中{ProcessorCacheHits}次, " +
                       $"耗时{ProcessingTime.TotalMilliseconds:F1}ms";
            }
        }

        /// <summary>
        /// 优化批量处理 - 现实项目场景优化
        /// 核心策略：按目录分组，利用相同目录文件的Profile共享特性
        /// </summary>
        public static Dictionary<string, List<AssetProcessor>> OptimizedBatchProcessing(
            AssetTree assetTree, 
            IEnumerable<string> assetPaths,
            out BatchStatistics statistics)
        {
            var startTime = DateTime.Now;
            var result = new Dictionary<string, List<AssetProcessor>>();
            var stats = new BatchStatistics();

            var validPaths = assetPaths?.Where(p => !string.IsNullOrEmpty(p)).Distinct().ToList();
            if (validPaths == null || validPaths.Count == 0)
            {
                statistics = stats;
                return result;
            }

            stats.TotalFiles = validPaths.Count;

            // 按目录分组 - 现实项目中同目录文件通常有相同的处理规则
            var directoryGroups = GroupByDirectory(validPaths);
            stats.UniqueDirectories = directoryGroups.Count;

            // 目录级Profile缓存 - 避免重复查询
            var directoryProfileCache = new Dictionary<string, List<AssetProfile>>();

            foreach (var group in directoryGroups)
            {
                var directory = group.Key;
                var filesInDirectory = group.Value;

                // 获取目录的Profile（利用缓存）
                List<AssetProfile> matchingProfiles;
                if (directoryProfileCache.TryGetValue(directory, out matchingProfiles))
                {
                    stats.ProfileCacheHits++;
                }
                else
                {
                    matchingProfiles = new List<AssetProfile>();
                    assetTree.GetMatchingProfiles(directory + "/dummy", matchingProfiles);
                    directoryProfileCache[directory] = matchingProfiles;
                }

                // 为目录中的每个文件计算处理器
                foreach (var assetPath in filesInDirectory)
                {
                    var processors = new List<AssetProcessor>(16);
                    
                    // 应用Profile到具体文件
                    foreach (var profile in matchingProfiles)
                    {
                        if (profile?.Enabled == true)
                        {
                            profile.GetMatchingProcessors(assetPath, processors);
                        }
                    }

                    // 过滤有效处理器
                    var validProcessors = FilterValidProcessors(processors, assetPath);
                    result[assetPath] = validProcessors;
                }
            }

            stats.ProcessingTime = DateTime.Now - startTime;
            statistics = stats;

            Logger.Debug(LogModule.Core, $"[BatchProcessingOptimizer] {stats}");
            return result;
        }

        /// <summary>
        /// 按目录分组文件路径
        /// </summary>
        private static Dictionary<string, List<string>> GroupByDirectory(List<string> assetPaths)
        {
            var directoryGroups = new Dictionary<string, List<string>>();

            foreach (var assetPath in assetPaths)
            {
                var directory = ExtractDirectoryPath(assetPath);
                
                if (!directoryGroups.TryGetValue(directory, out var files))
                {
                    files = new List<string>();
                    directoryGroups[directory] = files;
                }
                files.Add(assetPath);
            }

            return directoryGroups;
        }

        /// <summary>
        /// 提取目录路径
        /// </summary>
        private static string ExtractDirectoryPath(string assetPath)
        {
            var normalizedPath = assetPath.Replace('\\', '/');
            var lastSlashIndex = normalizedPath.LastIndexOf('/');
            return lastSlashIndex >= 0 ? normalizedPath.Substring(0, lastSlashIndex) : "";
        }

        /// <summary>
        /// 过滤有效处理器
        /// </summary>
        private static List<AssetProcessor> FilterValidProcessors(List<AssetProcessor> processors, string assetPath)
        {
            var validProcessors = new List<AssetProcessor>(processors.Count);

            foreach (var processor in processors)
            {
                if (processor?.Enabled == true && processor.CanProcess(assetPath))
                {
                    validProcessors.Add(processor);
                }
            }

            return validProcessors;
        }

        /// <summary>
        /// 分析批量处理的效率 - 用于性能调优
        /// </summary>
        public static void AnalyzeBatchEfficiency(IEnumerable<string> assetPaths)
        {
            var validPaths = assetPaths?.Where(p => !string.IsNullOrEmpty(p)).ToList();
            if (validPaths == null || validPaths.Count == 0) return;

            var directoryGroups = GroupByDirectory(validPaths);
            var totalFiles = validPaths.Count;
            var uniqueDirectories = directoryGroups.Count;
            var avgFilesPerDirectory = (float)totalFiles / uniqueDirectories;

            Logger.Info(LogModule.Core, 
                $"[BatchProcessingOptimizer] 批量处理分析: " +
                $"{totalFiles}个文件, {uniqueDirectories}个目录, " +
                $"平均每目录{avgFilesPerDirectory:F1}个文件");

            // 分析目录分布
            var directorySizes = directoryGroups.Values.Select(files => files.Count).OrderByDescending(x => x).ToList();
            var top5Directories = directorySizes.Take(5).ToList();
            
            Logger.Debug(LogModule.Core, 
                $"[BatchProcessingOptimizer] 最大目录文件数: [{string.Join(", ", top5Directories)}]");

            // 效率评估
            if (avgFilesPerDirectory > 10)
            {
                Logger.Info(LogModule.Core, 
                    "[BatchProcessingOptimizer] 批量处理效率高 - 目录聚集度良好");
            }
            else if (avgFilesPerDirectory < 3)
            {
                Logger.Warning(LogModule.Core, 
                    "[BatchProcessingOptimizer] 批量处理效率低 - 文件过于分散");
            }
        }

        /// <summary>
        /// 预测批量处理性能 - 帮助用户了解处理时间
        /// </summary>
        public static TimeSpan PredictProcessingTime(int fileCount, int directoryCount)
        {
            // 基于经验的性能预测模型
            // 假设：每个目录查询Profile需要1ms，每个文件处理需要0.1ms
            var directoryProcessingTime = directoryCount * 1; // ms
            var fileProcessingTime = fileCount * 0.1; // ms
            var totalMs = directoryProcessingTime + fileProcessingTime;

            return TimeSpan.FromMilliseconds(totalMs);
        }
    }
}
