using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace AssetPipeline.Core
{


        /// <summary>
        /// 预计算方法 - 保持接口兼容性
        /// </summary>
        public void PrecomputeProcessorsByType()
        {
            // 当前版本不做预计算，直接使用Profile.GetMatchingProcessors
        }
    }

    /// <summary>
    /// Profile路径前缀的Trie字典树 - 核心优化：O(D)时间复杂度的路径匹配
    /// </summary>
    [System.Serializable]
    public class ProfilePathTrie
    {
        /// <summary>
        /// Trie树节点 - 支持通配符匹配
        /// </summary>
        private class TrieNode
        {
            public List<AssetProfile> MatchingProfiles { get; set; }
            public Dictionary<string, TrieNode> Children { get; set; }
            public bool HasDirectMatch { get; set; }

            // 通配符支持
            public TrieNode WildcardChild { get; set; } // * 通配符
            public TrieNode RecursiveWildcardChild { get; set; } // ** 递归通配符

            public TrieNode()
            {
                MatchingProfiles = new List<AssetProfile>();
                Children = new Dictionary<string, TrieNode>(StringComparer.OrdinalIgnoreCase);
                HasDirectMatch = false;
            }

            public void AddProfile(AssetProfile profile)
            {
                // 避免重复添加
                foreach (var existingProfile in MatchingProfiles)
                {
                    if (ReferenceEquals(existingProfile, profile))
                        return;
                }

                MatchingProfiles.Add(profile);
                HasDirectMatch = true;
            }

            public void Clear()
            {
                MatchingProfiles.Clear();
                HasDirectMatch = false;
                foreach (var child in Children.Values)
                {
                    child.Clear();
                }
                Children.Clear();
                WildcardChild?.Clear();
                RecursiveWildcardChild?.Clear();
                WildcardChild = null;
                RecursiveWildcardChild = null;
            }
        }

        private readonly TrieNode _root;
        private int _totalProfiles;
        private bool _isDirty;

        public ProfilePathTrie()
        {
            _root = new TrieNode();
            _totalProfiles = 0;
            _isDirty = true;
        }

        /// <summary>
        /// 构建Trie树 - 核心优化入口
        /// </summary>
        public void BuildFromProfiles(IEnumerable<AssetProfile> profiles)
        {
            Clear();

            foreach (var profile in profiles)
            {
                if (profile?.Enabled == true && !string.IsNullOrEmpty(profile.PathPrefix))
                {
                    InsertProfile(profile);
                }
            }

            _isDirty = false;
        }

        /// <summary>
        /// 插入Profile到Trie树
        /// </summary>
        private void InsertProfile(AssetProfile profile)
        {
            var pathPrefix = profile.PathPrefix.TrimEnd('/');
            if (string.IsNullOrEmpty(pathPrefix)) return;

            var segments = pathPrefix.Split('/');
            var currentNode = _root;

            foreach (var segment in segments)
            {
                if (string.IsNullOrEmpty(segment)) continue;

                if (segment == "*")
                {
                    if (currentNode.WildcardChild == null)
                    {
                        currentNode.WildcardChild = new TrieNode();
                    }
                    currentNode = currentNode.WildcardChild;
                }
                else if (segment == "**")
                {
                    if (currentNode.RecursiveWildcardChild == null)
                    {
                        currentNode.RecursiveWildcardChild = new TrieNode();
                    }
                    currentNode = currentNode.RecursiveWildcardChild;
                }
                else
                {
                    if (!currentNode.Children.TryGetValue(segment, out var childNode))
                    {
                        childNode = new TrieNode();
                        currentNode.Children[segment] = childNode;
                    }
                    currentNode = childNode;
                }
            }

            currentNode.AddProfile(profile);
            _totalProfiles++;
        }

        /// <summary>
        /// 获取目录匹配的Profile - 核心查询方法
        /// </summary>
        public void GetMatchingProfiles(string directory, List<AssetProfile> matchingProfiles)
        {
            if (string.IsNullOrEmpty(directory)) return;
            CollectMatchingProfiles(directory, matchingProfiles);
        }

        /// <summary>
        /// 提取目录路径
        /// </summary>
        private string ExtractDirectoryPath(string assetPath)
        {
            var normalizedPath = assetPath.Replace('\\', '/');
            var lastSlashIndex = normalizedPath.LastIndexOf('/');
            return lastSlashIndex >= 0 ? normalizedPath.Substring(0, lastSlashIndex) : "";
        }

        /// <summary>
        /// 收集匹配的Profile - 支持通配符匹配
        /// </summary>
        private void CollectMatchingProfiles(string directoryPath, List<AssetProfile> results)
        {
            if (string.IsNullOrEmpty(directoryPath))
            {
                if (_root.HasDirectMatch)
                {
                    results.AddRange(_root.MatchingProfiles);
                }
                return;
            }

            var segments = directoryPath.Split('/');

            // 收集根节点的Profile（全局规则）
            if (_root.HasDirectMatch)
            {
                results.AddRange(_root.MatchingProfiles);
            }

            // 递归匹配支持通配符
            CollectMatchingProfilesRecursive(_root, segments, 0, results);
        }

        /// <summary>
        /// 递归收集匹配的Profile - 核心匹配算法
        /// </summary>
        private void CollectMatchingProfilesRecursive(TrieNode node, string[] segments, int segmentIndex, List<AssetProfile> results)
        {
            if (segmentIndex >= segments.Length)
            {
                if (node.HasDirectMatch)
                {
                    results.AddRange(node.MatchingProfiles);
                }
                return;
            }

            var currentSegment = segments[segmentIndex];
            if (string.IsNullOrEmpty(currentSegment))
            {
                CollectMatchingProfilesRecursive(node, segments, segmentIndex + 1, results);
                return;
            }

            // 精确匹配
            if (node.Children.TryGetValue(currentSegment, out var exactChild))
            {
                if (exactChild.HasDirectMatch)
                {
                    results.AddRange(exactChild.MatchingProfiles);
                }
                CollectMatchingProfilesRecursive(exactChild, segments, segmentIndex + 1, results);
            }

            // 单层通配符匹配 (*)
            if (node.WildcardChild != null)
            {
                if (node.WildcardChild.HasDirectMatch)
                {
                    results.AddRange(node.WildcardChild.MatchingProfiles);
                }
                CollectMatchingProfilesRecursive(node.WildcardChild, segments, segmentIndex + 1, results);
            }

            // 递归通配符匹配 (**)
            if (node.RecursiveWildcardChild != null)
            {
                if (node.RecursiveWildcardChild.HasDirectMatch)
                {
                    results.AddRange(node.RecursiveWildcardChild.MatchingProfiles);
                }

                // 递归通配符可以匹配0到多个路径段
                for (int i = segmentIndex; i <= segments.Length; i++)
                {
                    CollectMatchingProfilesRecursive(node.RecursiveWildcardChild, segments, i, results);
                }
            }
        }

        /// <summary>
        /// 清除所有数据
        /// </summary>
        public void Clear()
        {
            _root.Clear();
            _totalProfiles = 0;
            _isDirty = true;
        }

        /// <summary>
        /// 标记为需要重建
        /// </summary>
        public void MarkDirty()
        {
            _isDirty = true;
        }

        /// <summary>
        /// 检查是否需要重建
        /// </summary>
        public bool IsDirty => _isDirty;
    }
}
